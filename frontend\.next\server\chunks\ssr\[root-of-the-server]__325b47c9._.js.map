{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/page.js"], "sourcesContent": ["import { Section } from \"@/components/layout\";\r\n\r\nexport default function Home() {\r\n  return (\r\n    <main>\r\n      {/* Главная секция с героем */}\r\n      <Section spacing=\"lg\" background=\"primary\">\r\n        <div className=\"text-center\">\r\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-gray-900\">\r\n            Добро пожаловать\r\n          </h2>\r\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\">\r\n            Основной контент страницы с современным дизайном и адаптивной версткой\r\n          </p>\r\n        </div>\r\n      </Section>\r\n\r\n      {/* Секция с контентом */}\r\n      <Section spacing=\"default\">\r\n        <div className=\"space-y-12\">\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 1</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 2</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </Section>\r\n\r\n      {/* Секция с альтернативным фоном */}\r\n      <Section spacing=\"lg\" background=\"muted\">\r\n        <div className=\"grid md:grid-cols-2 gap-8\">\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 3</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 4</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </Section>\r\n\r\n      {/* Финальная секция */}\r\n      <Section spacing=\"xl\" containerSize=\"sm\">\r\n        <div className=\"text-center bg-gradient-to-br from-primary/10 to-primary/5 p-12 rounded-3xl border border-primary/20\">\r\n          <h3 className=\"text-3xl font-semibold mb-6 text-gray-900\">Заключение</h3>\r\n          <p className=\"text-gray-700 text-lg leading-relaxed\">\r\n            Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.\r\n          </p>\r\n        </div>\r\n      </Section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BAEC,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;gBAAK,YAAW;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,8OAAC;4BAAE,WAAU;sCAAqD;;;;;;;;;;;;;;;;;0BAOtE,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;gBAAK,YAAW;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;gBAAK,eAAc;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}]}