{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderItem.jsx"], "sourcesContent": ["import {\r\n  User,\r\n  Phone,\r\n  Truck,\r\n  MessageSquare,\r\n  CreditCard,\r\n  TriangleAlert,\r\n  CheckCircle,\r\n  MapPin,\r\n  Package\r\n} from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderItem({ order }) {\r\n  // Определяем стили и иконки для статуса\r\n  const getStatusConfig = (status) => {\r\n    const configs = {\r\n      new: {\r\n        icon: TriangleAlert,\r\n        label: \"Новый\",\r\n        className: \"bg-yellow-100 text-yellow-800 border-yellow-200\"\r\n      },\r\n      confirmed: {\r\n        icon: CheckCircle,\r\n        label: \"Подтвержден\",\r\n        className: \"bg-blue-100 text-blue-800 border-blue-200\"\r\n      },\r\n      completed: {\r\n        icon: CheckCircle,\r\n        label: \"Выполнен\",\r\n        className: \"bg-green-100 text-green-800 border-green-200\"\r\n      },\r\n      cancelled: {\r\n        icon: TriangleAlert,\r\n        label: \"Отменен\",\r\n        className: \"bg-red-100 text-red-800 border-red-200\"\r\n      }\r\n    };\r\n    return configs[status] || configs.pending;\r\n  };\r\n\r\n  // Определяем иконку для способа оплаты\r\n  const getPaymentLabel = (system) => {\r\n    const labels = {\r\n      banktransfer: \"Банковский перевод\",\r\n      cash: \"Наличные\",\r\n      card: \"Банковская карта\"\r\n    };\r\n    return labels[system] || system;\r\n  };\r\n\r\n  const statusConfig = getStatusConfig(order.status);\r\n  const StatusIcon = statusConfig.icon;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300\">\r\n      {/* Заголовок с номером заказа и статусом */}\r\n      <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900\">\r\n            Заказ #{order.id || \"**********\"}\r\n          </h3>\r\n          <div className={cn(\r\n            \"inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border\",\r\n            statusConfig.className\r\n          )}>\r\n            <StatusIcon className=\"w-4 h-4\" />\r\n            {statusConfig.label}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-6 space-y-6\">\r\n        {/* Информация о клиенте */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <User className=\"w-5 h-5 text-gray-600\" />\r\n            Информация о клиенте\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4 space-y-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <User className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900 font-medium\">{order.user.first_name}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-3\">\r\n              <Phone className=\"w-4 h-4 text-gray-500\" />\r\n              <a\r\n                href={`tel:${order.user.phone}`}\r\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\r\n              >\r\n                {order.user.phone}\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Детали доставки */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <Truck className=\"w-5 h-5 text-gray-600\" />\r\n            Доставка\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4 space-y-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <MapPin className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900\">{order.delivery_type}</span>\r\n            </div>\r\n            {order.address && (\r\n              <div className=\"flex items-start gap-3\">\r\n                <MapPin className=\"w-4 h-4 text-gray-500 mt-0.5\" />\r\n                <span className=\"text-gray-700\">{order.address}</span>\r\n              </div>\r\n            )}\r\n            {order.comment && (\r\n              <div className=\"flex items-start gap-3\">\r\n                <MessageSquare className=\"w-4 h-4 text-gray-500 mt-0.5\" />\r\n                <span className=\"text-gray-700 italic\">\"{order.comment}\"</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Способ оплаты */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <CreditCard className=\"w-5 h-5 text-gray-600\" />\r\n            Способ оплаты\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <CreditCard className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900\">{getPaymentLabel(order.paymentsystem)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Список блюд */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <Package className=\"w-5 h-5 text-gray-600\" />\r\n            Состав заказа\r\n          </h4>\r\n          <div className=\"space-y-3\">\r\n            {order.meals.map((meal) => (\r\n              <div\r\n                key={meal.id}\r\n                className=\"bg-gray-50 rounded-xl p-4 flex items-center gap-4 hover:bg-gray-100 transition-colors\"\r\n              >\r\n                {meal.img && (\r\n                  <div className=\"flex-shrink-0\">\r\n                    <img\r\n                      src={meal.img}\r\n                      alt={meal.name}\r\n                      className=\"w-16 h-16 rounded-lg object-cover border border-gray-200\"\r\n                    />\r\n                  </div>\r\n                )}\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <h5 className=\"font-medium text-gray-900 truncate\">{meal.name}</h5>\r\n                  <div className=\"flex items-center gap-4 mt-1 text-sm text-gray-600\">\r\n                    <span>{meal.quantity} {meal.unit}</span>\r\n                    {meal.portion && (\r\n                      <span>• Порция: {meal.portion} {meal.unit}</span>\r\n                    )}\r\n                    <span>• {meal.price} ₽/{meal.unit}</span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <div className=\"text-lg font-semibold text-gray-900\">\r\n                    {meal.amount} ₽\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Итоговая сумма */}\r\n        <div className=\"border-t border-gray-200 pt-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-lg font-semibold text-gray-900\">Итого к оплате:</span>\r\n            <span className=\"text-2xl font-bold text-gray-900\">{order.amount} ₽</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;;AAEe,SAAS,UAAU,EAAE,KAAK,EAAE;IACzC,wCAAwC;IACxC,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU;YACd,KAAK;gBACH,MAAM,wNAAA,CAAA,gBAAa;gBACnB,OAAO;gBACP,WAAW;YACb;YACA,WAAW;gBACT,MAAM,2NAAA,CAAA,cAAW;gBACjB,OAAO;gBACP,WAAW;YACb;YACA,WAAW;gBACT,MAAM,2NAAA,CAAA,cAAW;gBACjB,OAAO;gBACP,WAAW;YACb;YACA,WAAW;gBACT,MAAM,wNAAA,CAAA,gBAAa;gBACnB,OAAO;gBACP,WAAW;YACb;QACF;QACA,OAAO,OAAO,CAAC,OAAO,IAAI,QAAQ,OAAO;IAC3C;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,cAAc;YACd,MAAM;YACN,MAAM;QACR;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,eAAe,gBAAgB,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCAC1C,MAAM,EAAE,IAAI;;;;;;;sCAEtB,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,sFACA,aAAa,SAAS;;8CAEtB,8OAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;;;;;;0BAKzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAA6B,MAAM,IAAI,CAAC,UAAU;;;;;;;;;;;;kDAEpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDACC,MAAM,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,KAAK,EAAE;gDAC/B,WAAU;0DAET,MAAM,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAiB,MAAM,aAAa;;;;;;;;;;;;oCAErD,MAAM,OAAO,kBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAiB,MAAM,OAAO;;;;;;;;;;;;oCAGjD,MAAM,OAAO,kBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAK,WAAU;;oDAAuB;oDAAE,MAAM,OAAO;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAiB,gBAAgB,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAM1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG/C,8OAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC;wCAEC,WAAU;;4CAET,KAAK,GAAG,kBACP,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK,KAAK,GAAG;oDACb,KAAK,KAAK,IAAI;oDACd,WAAU;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsC,KAAK,IAAI;;;;;;kEAC7D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAM,KAAK,QAAQ;oEAAC;oEAAE,KAAK,IAAI;;;;;;;4DAC/B,KAAK,OAAO,kBACX,8OAAC;;oEAAK;oEAAW,KAAK,OAAO;oEAAC;oEAAE,KAAK,IAAI;;;;;;;0EAE3C,8OAAC;;oEAAK;oEAAG,KAAK,KAAK;oEAAC;oEAAI,KAAK,IAAI;;;;;;;;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,MAAM;wDAAC;;;;;;;;;;;;;uCAxBZ,KAAK,EAAE;;;;;;;;;;;;;;;;kCAiCpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;8CACtD,8OAAC;oCAAK,WAAU;;wCAAoC,MAAM,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7E", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/page.js"], "sourcesContent": ["import { Section } from \"@/components/layout\";\r\nimport OrderItem from \"@/components/shared/OrderItem\";\r\n\r\nexport default function Home() {\r\n  // Преобразованные данные заказа для компонента OrderItem\r\n  const orderData = {\r\n    id: \"**********\",\r\n    user: {\r\n      first_name: \"<PERSON>а<PERSON><PERSON>\",\r\n      phone: \"+7 (950) 079-32-65\"\r\n    },\r\n    delivery_type: \"Самовывоз\",\r\n    comment: \"Тестовый заказ, не пробовать\",\r\n    address: \"\", // Пустой для самовывоза\r\n    paymentsystem: \"banktransfer\",\r\n    status: \"cancelled\", // Добавляем статус по умолчанию\r\n    amount: \"1400\",\r\n    meals: [\r\n      {\r\n        id: \"ehnOzTB06KH0dpL2HiZP\",\r\n        name: \"Окрошка на Квасе\",\r\n        quantity: 1400,\r\n        amount: 1400,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      }\r\n    ]\r\n  };\r\n\r\n  return (\r\n    <main>\r\n      {/* Главная секция с героем */}\r\n      <Section spacing=\"lg\">\r\n        <div className=\"text-center\">\r\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-gray-900\">\r\n            Добро пожаловать\r\n          </h2>\r\n          <p className=\"text-lg md:text-xl text-gray-600 max-w-2xl mx-auto\">\r\n            Основной контент страницы с современным дизайном и адаптивной версткой\r\n          </p>\r\n        </div>\r\n      </Section>\r\n\r\n      <Section>\r\n        <OrderItem order={orderData} />\r\n      </Section>\r\n\r\n      {/* Секция с контентом */}\r\n      <Section spacing=\"default\">\r\n        <div className=\"space-y-12\">\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 1</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 2</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </Section>\r\n\r\n      {/* Секция с альтернативным фоном */}\r\n      <Section spacing=\"lg\" background=\"muted\">\r\n        <div className=\"grid md:grid-cols-2 gap-8\">\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 3</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"bg-white p-8 rounded-2xl shadow-sm border border-gray-100\">\r\n            <h3 className=\"text-2xl font-semibold mb-6 text-gray-900\">Раздел 4</h3>\r\n            <p className=\"text-gray-700 mb-4 leading-relaxed\">\r\n              At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.\r\n            </p>\r\n            <p className=\"text-gray-700 leading-relaxed\">\r\n              Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </Section>\r\n\r\n      {/* Финальная секция */}\r\n      <Section spacing=\"xl\" containerSize=\"sm\">\r\n        <div className=\"text-center bg-gradient-to-br from-primary/10 to-primary/5 p-12 rounded-3xl border border-primary/20\">\r\n          <h3 className=\"text-3xl font-semibold mb-6 text-gray-900\">Заключение</h3>\r\n          <p className=\"text-gray-700 text-lg leading-relaxed\">\r\n            Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.\r\n          </p>\r\n        </div>\r\n      </Section>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;;AAEe,SAAS;IACtB,yDAAyD;IACzD,MAAM,YAAY;QAChB,IAAI;QACJ,MAAM;YACJ,YAAY;YACZ,OAAO;QACT;QACA,eAAe;QACf,SAAS;QACT,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,qBACE,8OAAC;;0BAEC,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,8OAAC;4BAAE,WAAU;sCAAqD;;;;;;;;;;;;;;;;;0BAMtE,8OAAC,6KAAA,CAAA,UAAO;0BACN,cAAA,8OAAC,yIAAA,CAAA,UAAS;oBAAC,OAAO;;;;;;;;;;;0BAIpB,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;gBAAK,YAAW;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC,6KAAA,CAAA,UAAO;gBAAC,SAAQ;gBAAK,eAAc;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAC1D,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}]}