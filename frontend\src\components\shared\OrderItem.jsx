import {
  User,
  Phone,
  Truck,
  MessageSquare,
  CreditCard,
  TriangleAlert,
  CheckCircle,
  MapPin,
  Package
} from "lucide-react";
import { cn } from "@/lib/utils";

export default function OrderItem({ order }) {
  // Определяем стили и иконки для статуса
  const getStatusConfig = (status) => {
    const configs = {
      new: {
        icon: TriangleAlert,
        label: "Новый",
        className: "bg-yellow-100 text-yellow-800 border-yellow-200"
      },
      confirmed: {
        icon: CheckCircle,
        label: "Подтвержден",
        className: "bg-blue-100 text-blue-800 border-blue-200"
      },
      completed: {
        icon: CheckCircle,
        label: "Выполнен",
        className: "bg-green-100 text-green-800 border-green-200"
      },
      cancelled: {
        icon: TriangleAlert,
        label: "Отменен",
        className: "bg-red-100 text-red-800 border-red-200"
      }
    };
    return configs[status] || configs.pending;
  };

  // Определяем иконку для способа оплаты
  const getPaymentLabel = (system) => {
    const labels = {
      banktransfer: "Банковский перевод",
      cash: "Наличные",
      card: "Банковская карта"
    };
    return labels[system] || system;
  };

  const statusConfig = getStatusConfig(order.status);
  const StatusIcon = statusConfig.icon;

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* Заголовок с номером заказа и статусом */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Заказ #{order.id || "**********"}
          </h3>
          <div className={cn(
            "inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border",
            statusConfig.className
          )}>
            <StatusIcon className="w-4 h-4" />
            {statusConfig.label}
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Информация о клиенте */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <User className="w-5 h-5 text-gray-600" />
            Информация о клиенте
          </h4>
          <div className="bg-gray-50 rounded-xl p-4 space-y-3">
            <div className="flex items-center gap-3">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900 font-medium">{order.user.first_name}</span>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-gray-500" />
              <a
                href={`tel:${order.user.phone}`}
                className="text-blue-600 hover:text-blue-800 transition-colors"
              >
                {order.user.phone}
              </a>
            </div>
          </div>
        </div>

        {/* Детали доставки */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <Truck className="w-5 h-5 text-gray-600" />
            Доставка
          </h4>
          <div className="bg-gray-50 rounded-xl p-4 space-y-3">
            <div className="flex items-center gap-3">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900">{order.delivery_type}</span>
            </div>
            {order.address && (
              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
                <span className="text-gray-700">{order.address}</span>
              </div>
            )}
            {order.comment && (
              <div className="flex items-start gap-3">
                <MessageSquare className="w-4 h-4 text-gray-500 mt-0.5" />
                <span className="text-gray-700 italic">"{order.comment}"</span>
              </div>
            )}
          </div>
        </div>

        {/* Способ оплаты */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-gray-600" />
            Способ оплаты
          </h4>
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900">{getPaymentLabel(order.paymentsystem)}</span>
            </div>
          </div>
        </div>

        {/* Список блюд */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <Package className="w-5 h-5 text-gray-600" />
            Состав заказа
          </h4>
          <div className="space-y-3">
            {order.meals.map((meal) => (
              <div
                key={meal.id}
                className="bg-gray-50 rounded-xl p-4 flex items-center gap-4 hover:bg-gray-100 transition-colors"
              >
                {meal.img && (
                  <div className="flex-shrink-0">
                    <img
                      src={meal.img}
                      alt={meal.name}
                      className="w-16 h-16 rounded-lg object-cover border border-gray-200"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <h5 className="font-medium text-gray-900 truncate">{meal.name}</h5>
                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                    <span>{meal.quantity} {meal.unit}</span>
                    {meal.portion && (
                      <span>• Порция: {meal.portion} {meal.unit}</span>
                    )}
                    <span>• {meal.price} ₽/{meal.unit}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900">
                    {meal.amount} ₽
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Итоговая сумма */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-900">Итого к оплате:</span>
            <span className="text-2xl font-bold text-gray-900">{order.amount} ₽</span>
          </div>
        </div>
      </div>
    </div>
  );
}