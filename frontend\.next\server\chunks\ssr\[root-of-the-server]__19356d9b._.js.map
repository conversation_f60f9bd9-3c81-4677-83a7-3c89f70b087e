{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/bagel_fat_one_d0b28e80.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"bagel_fat_one_d0b28e80-module__1RWCdq__className\",\n  \"variable\": \"bagel_fat_one_d0b28e80-module__1RWCdq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/bagel_fat_one_d0b28e80.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Bagel_Fat_One%22,%22arguments%22:[{%22variable%22:%22--font-bagel-fat-one%22,%22weight%22:%22400%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22bagelFatOne%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Bagel Fat One', 'Bagel Fat One Fallback'\",\n        fontWeight: 400,\nfontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,YAAY;QACpB,WAAW;IAEP;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Container.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\nexport default function Container({\r\n  children,\r\n  className,\r\n  size = \"default\",\r\n  as: Component = \"div\",\r\n  ...props\r\n}) {\r\n  const sizeVariants = {\r\n    sm: \"max-w-4xl\",\r\n    default: \"max-w-6xl\",\r\n    lg: \"max-w-7xl\",\r\n    full: \"max-w-none\"\r\n  };\r\n\r\n  return (\r\n    <Component\r\n      className={cn(\r\n        \"w-full mx-auto transition-all duration-300 ease-in-out\",\r\n\r\n        sizeVariants[size],\r\n\r\n        \"px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16\",\r\n\r\n        \"relative\",\r\n\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Component>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,UAAU,EAChC,QAAQ,EACR,SAAS,EACT,OAAO,SAAS,EAChB,IAAI,YAAY,KAAK,EACrB,GAAG,OACJ;IACC,MAAM,eAAe;QACnB,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DAEA,YAAY,CAAC,KAAK,EAElB,0CAEA,YAEA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Header.jsx"], "sourcesContent": ["import Container from './Container';\r\n\r\nexport default function Header() {\r\n    return (\r\n        <header className=\"sticky top-0 z-50 bg-white/10 backdrop-blur-md\">\r\n            <Container className=\"py-4 relative z-10\">\r\n              <h1 className=\"text-2xl font-bagel-fat-one text-gray-900\">\r\n                VKUS\r\n              </h1>\r\n            </Container>\r\n        </header>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACpB,qBACI,8OAAC;QAAO,WAAU;kBACd,cAAA,8OAAC,yIAAA,CAAA,UAAS;YAAC,WAAU;sBACnB,cAAA,8OAAC;gBAAG,WAAU;0BAA4C;;;;;;;;;;;;;;;;AAMxE", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/layout.js"], "sourcesContent": ["import { Bagel_Fat_One } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport Header from \"@/components/layout/Header\";\r\n\r\nconst bagelFatOne = Bagel_Fat_One({\r\n  variable: \"--font-bagel-fat-one\",\r\n  weight: \"400\",\r\n  subsets: [\"latin\"],\r\n});\r\n\r\nexport const metadata = {\r\n  title: \"VKUS\",\r\n  description: \"VKUS application\",\r\n};\r\n\r\nexport default function RootLayout({ children }) {\r\n  return (\r\n    <html lang=\"ru\">\r\n      <body\r\n        className={`${bagelFatOne.variable} antialiased`}\r\n      >\r\n        <div className=\"min-h-screen flex flex-col\">\r\n          <Header />\r\n          <main className=\"flex-1\">\r\n            {children}\r\n          </main>\r\n        </div>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAQO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,iJAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEhD,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}